import create from 'zustand';
import axios from 'axios';
import { apiQuotesPath } from 'routes';

const QuotePaginationLimit = 10;

const initialState = {
  loadingQuotes: false,
  loadingMore: false,
  hasMore: true,
  quotes: [],
  page: 1,
};

const customerQuotesStore = create((set, get) => ({
  ...initialState,
  fetchQuotes: async ({ page }) => {
    set({ loadingQuotes: page == 1, loadingMore: page > 1 });

    const { data: quotes } = await axios({
      method: 'GET',
      url: apiQuotesPath({ format: 'json' }),
      params: { page },
    });

    set((state) => ({
      quotes: [...state.quotes, ...quotes],
      page: page + 1,
      hasMore: quotes.length == QuotePaginationLimit,
      loadingQuotes: false,
      loadingMore: false,
    }));
  },
}));

export default customerQuotesStore;
