import PropTypes from 'prop-types';
import ReactTooltip from 'react-tooltip';

const BusinessCertifications = ({ formData, errors, updateFormData }) => {
  // Tooltip content for business certifications
  const certificationTooltips = {
    environmental:
      'ISO 14001 Environmental Management System certification or other recognized environmental accreditations that demonstrate your commitment to environmental responsibility and sustainable practices.',
    indigenous:
      'Supply Nation certification verifies that your business is at least 51% owned, operated and controlled by Indigenous Australians, supporting Indigenous economic development.',
    charity:
      'Australian Charities and Not-for-profits Commission (ACNC) registration confirms your organization operates for charitable purposes and meets regulatory requirements.',
    social:
      'Social Trader or Social Enterprise World Forum (SEWF) certification recognizes businesses that trade for social and/or environmental purpose, reinvesting profits back into their mission.',
    female:
      "Certification that your business has greater than 50% female ownership, supporting gender diversity and women's economic empowerment in business.",
    rainforest:
      'Rainforest Alliance certification ensures your products meet rigorous environmental, social, and economic standards that protect forests, improve livelihoods, and promote sustainable agriculture.',
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    updateFormData(name, type === 'checkbox' ? checked : value);
  };

  return (
    <div>
      <h3 className="login-heading">Business Certifications (Optional)</h3>

      <div className="row">
        <div className="small-12 columns">
          <p className="help-text">Select any certifications or characteristics that apply to your business:</p>
        </div>
      </div>

      {/* Environmental Accreditation */}
      <div className="row">
        <div className="small-12 columns mb-1 no-gutter">
          <label className="mt-1-2">
            <div className="section-toggle gutter">
              <input
                type="checkbox"
                name="is_environmentally_accredited"
                checked={formData.is_environmentally_accredited}
                onChange={handleChange}
                id="is-environmentally-accredited"
              />
              <span className="section-toggle__switch" />
            </div>
            Environmental Accreditation
            <span
              className="whats-this ml-1 bottom tooltip-dash"
              style={{ float: 'right' }}
              data-tip={certificationTooltips.environmental}
              data-for="environmental-tooltip"
            >
              What's This?
            </span>
          </label>
        </div>
      </div>

      {/* Indigenous Owned */}
      <div className="row">
        <div className="small-12 columns mb-1 no-gutter">
          <label className="mt-1-2">
            <div className="section-toggle gutter">
              <input
                type="checkbox"
                name="is_indigenous_owned"
                checked={formData.is_indigenous_owned}
                onChange={handleChange}
                id="is-indigenous-owned"
              />
              <span className="section-toggle__switch" />
            </div>
            Indigenous Owned
            <span
              className="whats-this ml-1 bottom tooltip-dash"
              style={{ float: 'right' }}
              data-tip={certificationTooltips.indigenous}
              data-for="indigenous-tooltip"
            >
              What's This?
            </span>
          </label>
        </div>
      </div>

      {/* Registered Charity */}
      <div className="row">
        <div className="small-12 columns mb-1 no-gutter">
          <label className="mt-1-2">
            <div className="section-toggle gutter">
              <input
                type="checkbox"
                name="is_registered_charity"
                checked={formData.is_registered_charity}
                onChange={handleChange}
                id="is-registered-charity"
              />
              <span className="section-toggle__switch" />
            </div>
            Registered Charity
            <span
              className="whats-this ml-1 bottom tooltip-dash"
              style={{ float: 'right' }}
              data-tip={certificationTooltips.charity}
              data-for="charity-tooltip"
            >
              What's This?
            </span>
          </label>
        </div>
      </div>

      {/* Social Enterprise */}
      <div className="row">
        <div className="small-12 columns mb-1 no-gutter">
          <label className="mt-1-2">
            <div className="section-toggle gutter">
              <input
                type="checkbox"
                name="is_socially_responsible"
                checked={formData.is_socially_responsible}
                onChange={handleChange}
                id="is-socially-responsible"
              />
              <span className="section-toggle__switch" />
            </div>
            Social Enterprise
            <span
              className="whats-this ml-1 bottom tooltip-dash"
              style={{ float: 'right' }}
              data-tip={certificationTooltips.social}
              data-for="social-tooltip"
            >
              What's This?
            </span>
          </label>
        </div>
      </div>

      {/* Female Owned */}
      <div className="row">
        <div className="small-12 columns mb-1 no-gutter">
          <label className="mt-1-2">
            <div className="section-toggle gutter">
              <input
                type="checkbox"
                name="is_female_owned"
                checked={formData.is_female_owned}
                onChange={handleChange}
                id="is-female-owned"
              />
              <span className="section-toggle__switch" />
            </div>
            Female Owned
            <span
              className="whats-this ml-1 bottom tooltip-dash"
              style={{ float: 'right' }}
              data-tip={certificationTooltips.female}
              data-for="female-tooltip"
            >
              What's This?
            </span>
          </label>
        </div>
      </div>

      {/* Rainforest Alliance */}
      <div className="row">
        <div className="small-12 columns mb-1 no-gutter">
          <label className="mt-1-2">
            <div className="section-toggle gutter">
              <input
                type="checkbox"
                name="is_rainforest_alliance_certified"
                checked={formData.is_rainforest_alliance_certified}
                onChange={handleChange}
                id="is-rainforest-alliance-certified"
              />
              <span className="section-toggle__switch" />
            </div>
            Rainforest Alliance
            <span
              className="whats-this ml-1 bottom tooltip-dash"
              style={{ float: 'right' }}
              data-tip={certificationTooltips.rainforest}
              data-for="rainforest-tooltip"
            >
              What's This?
            </span>
          </label>
        </div>
      </div>

      {errors.general && (
        <div className="row">
          <div className="small-12 columns">
            <label className="form-error is-visible">{errors.general}</label>
          </div>
        </div>
      )}

      {/* React Tooltips */}
      <ReactTooltip id="environmental-tooltip" place="bottom" effect="solid" className="whats-this__tooltip" />
      <ReactTooltip id="indigenous-tooltip" place="bottom" effect="solid" className="whats-this__tooltip" />
      <ReactTooltip id="charity-tooltip" place="bottom" effect="solid" className="whats-this__tooltip" />
      <ReactTooltip id="social-tooltip" place="bottom" effect="solid" className="whats-this__tooltip" />
      <ReactTooltip id="female-tooltip" place="bottom" effect="solid" className="whats-this__tooltip" />
      <ReactTooltip id="rainforest-tooltip" place="bottom" effect="solid" className="whats-this__tooltip" />
    </div>
  );
};

BusinessCertifications.propTypes = {
  formData: PropTypes.object.isRequired,
  errors: PropTypes.object.isRequired,
  updateFormData: PropTypes.func.isRequired,
};

export default BusinessCertifications;
