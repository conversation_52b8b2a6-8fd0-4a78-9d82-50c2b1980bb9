import { useState, useEffect, useRef } from 'react';

// store
import shallow from 'zustand/shallow';
import invoiceSummaryAdminStore from 'store/admin/invoiceSummaryAdminStore';

// components
import InvoiceCustomer from './InvoiceCustomer';
import SummaryActions from './SummaryActions';
import SummarySkeleton from './SummarySkeleton';
import SummaryFilters from './SummaryFilters';
import NoSummaryNotice from './NoSummaryNotice';
import SummaryDetails from './SummaryDetails';

const InvoiceSummaryAdminApp = () => {
  const isMounted = useRef(false);
  const [activeSummary, setActiveSummary] = useState(null);

  const { setFilters, fetchAgedReceivables, agedReceivables, loadingList } = invoiceSummaryAdminStore(
    (state) => ({
      setFilters: state.setFilters,
      fetchAgedReceivables: state.fetchAgedReceivables,
      agedReceivables: state.agedReceivables,
      loadingList: state.loadingList,
    }),
    shallow
  );

  useEffect(() => {
    const existingFilters = JSON.parse(localStorage.getItem('InvoiceSummaryFilters') || JSON.stringify({}));

    setFilters({
      ...existingFilters,
    });
    fetchAgedReceivables();
  }, []);

  return (
    <>
      {!!activeSummary && (
        <>
          <div className="overlay show" onClick={() => setActiveSummary(null)} />
          <div className={`sidebar-overlay ${activeSummary ? 'open' : 'closed'}`}>
            <div className="admin-order-slider">
              <SummaryDetails agedReceivable={activeSummary} setActiveSummary={setActiveSummary} />
            </div>
          </div>
        </>
      )}
      <SummaryActions />
      <div className="sticky-container">
        <SummaryFilters />
        <div className="item-list__headings sticky">
          <span className="list-flex-1" />
          <span className="list-flex-2">Customer</span>
          <span className="list-flex-2 text-center">Current</span>
          <span className="list-flex-2 text-center">{'< 1 month'}</span>
          <span className="list-flex-2 text-center">1 month</span>
          <span className="list-flex-2 text-center">2 months</span>
          <span className="list-flex-2 text-center">3 months</span>
          <span className="list-flex-2 text-center">Older</span>
          <span className="list-flex-2 text-center">Total</span>
        </div>
      </div>
      <NoSummaryNotice isLoading={loadingList} agedReceivables={agedReceivables} />
      {agedReceivables.map((agedReceivable, idx) => (
        <InvoiceCustomer
          key={`aged-receivable-${agedReceivable.id}`}
          agedReceivable={agedReceivable}
          index={idx}
          setActiveSummary={setActiveSummary}
        />
      ))}
      <div>{loadingList && <SummarySkeleton />}</div>
    </>
  );
};

export default InvoiceSummaryAdminApp;
