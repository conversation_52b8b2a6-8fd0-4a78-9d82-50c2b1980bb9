import { useContext } from 'react';
import shallow from 'zustand/shallow';

import invoiceSummaryAdminStore from 'store/admin/invoiceSummaryAdminStore';
import appContext from 'contexts/appContext';

const SummaryFilters = () => {
  const { favouritesOnly, setFavourites } = invoiceSummaryAdminStore(
    (state) => ({
      favouritesOnly: state.favouritesOnly,
      setFavourites: state.setFavourites,
    }),
    shallow
  );
  const { hasFavourites } = useContext(appContext);

  if (!hasFavourites) {
    return null;
  }

  return (
    <div className="order-list-options">
      <span style={{ paddingRight: '8px' }}>Filters: </span>
      <label className="drop-text admin-order-option between-flex">
        <input
          type="checkbox"
          name="show-favourites"
          className="checkbox-content"
          checked={favouritesOnly}
          onChange={() => setFavourites(!favouritesOnly)}
        />
        <span className="checkbox-content-tick" style={{ backgroundColor: '#ffffff' }} />
        Favourite Customer Invoices
      </label>
    </div>
  );
};

export default SummaryFilters;
