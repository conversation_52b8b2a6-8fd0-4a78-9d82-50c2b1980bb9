import { useContext } from 'react'; 
import PropTypes from 'prop-types';

import appContext from 'contexts/appContext';
import shallow from 'zustand/shallow';
import useCustomerQuoteStore from 'store/customerQuotesStore';
import { getCircleIconColor } from 'javascript/utilities/getCircleIconColor';

const Quote = ({ quote, index, setActiveQuote }) => {
  const circleColor = getCircleIconColor(index);
  const { externalOrderUrls } = useContext(appContext);

  const startNewOrder = () => {
    debugger
    const date = quote.Date.split(' ')[2];
    const time = quote.Time
  }

  let newOrderUrl;
  if (['submitted', 'quoted'].includes(quote.status)) {
     newOrderUrl = ['catering', 'event'].includes(quote.kind) ? externalOrderUrls.catering : externalOrderUrls.pantry;
    newOrderUrl += `?quoteUUID=${quote.uuid}`;
    if (quote.Date) {
      const forDate = quote.Date.split(' ')[2];
      newOrderUrl += `&for_date=${forDate}`
    }  
  }

  return (
    <>
      <div className="customer-invoice customer-data">
        <div className="list-flex-1">
          <span className="circle-icon" style={{ background: circleColor }}>
            {quote.number_of_orders || '-'}
          </span>
        </div>
        <div className="list-flex-2">
          <strong>{quote.kind.toUpperCase()}</strong>
        </div>
        <div className="list-flex-2 text-center">{quote.created_at}</div>
        <div className="list-flex-2 text-center">{quote.status.toUpperCase()}</div>
        <div className="list-flex-1">
          <a onClick={() => setActiveQuote(quote)}>View</a>
          {newOrderUrl && (
            <a className='ml-1-2' href={newOrderUrl} target='_blank'>
              New Order
            </a>
          )}
        </div>
      </div>
    </>
  );
};

Quote.propTypes = {
  quote: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
  setActiveQuote: PropTypes.func.isRequired,
};

export default Quote;
