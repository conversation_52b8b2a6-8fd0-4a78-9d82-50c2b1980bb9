import { useContext } from 'react';

import appContext from 'contexts/appContext';

const CalendarOrderCreatePopup = ({ date }) => {
  const { externalOrderUrls } = useContext(appContext);

  return (
    <div className="popup-order-create">
      <p style={{ marginBottom: 0, fontWeight: 'bold' }}>{date}</p>
      <a className="order-link catering" href={externalOrderUrls.catering}>
        New Catering Order
      </a>
      <a className="order-link snacks" href={externalOrderUrls.pantry}>
        New Pantry Order
      </a>
    </div>
  );
};

export default CalendarOrderCreatePopup;
