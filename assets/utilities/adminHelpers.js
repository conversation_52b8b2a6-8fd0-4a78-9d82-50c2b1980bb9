import { formatDistanceToNow } from 'date-fns';

export const companyFlags = {
  can_pay_on_account: 'Pay On Account',
  can_pay_by_credit_card: 'Pay By Card',
  requires_po: 'Requires PO number',
  invoice_by_po: 'Invoice By PO',
};

export const adminFields = {
  super_admin: 'Is Super Admin',
  admin: 'Is Yordar Admin',
  can_access_suppliers: 'Is Supplier Admin',
};

export const paymentTermOptions = [7, 14, 21, 30, 45, 60];

export const accountingSoftwareOptions = {
  '': '',
  ariba: 'Ariba',
  corrigo: 'Corrigo',
  coupa: 'Coupa',
};

export const couponTypes = {
  amount: 'Amount',
  percentage: 'Percentage',
};

export const reminderFrequencies = {
  weekly: 'Weekly',
  fortnightly: 'Fortnightly',
  monthly_by_date: 'Monthly By Date',
  monthly_by_weekday: 'Monthly By Weekday',
};

export const reminderRecipients = {
  account_manager: 'Account Managers',
  pantry_manager: 'Pantry Managers',
  accounts_team: 'Accounts Team',
}

export const statesOptions = ['NSW', 'VIC', 'QLD', 'WA', 'SA', 'ACT'];
export const contryCodeOptions = ['AU', 'NZ'];

export const promotionKinds = {
  amount: 'Amount',
  percentage: 'Percentage',
};

export const promotionCategoryOptions = {
  'catering-services': 'Catering',
  'kitchen-supplies': 'Pantry',
};

export const permissionScopes = {
  company_team_admin: 'Company Team Admin',
  pantry_manager: 'Pantry Manager',
  account_manager: 'Account Manager',
};

export const deliveryOverrideKinds = {
  catering: 'All catering orders',
  pantry: 'All pantry orders',
  specific: 'Supplier Specific',
}

const eventFields = ['Location', 'Date', 'Time', 'Occasion', 'Budget', 'Budget Type', 'Estimated Attendees'];
const requirementFields = [
  'Categories',
  'Allergies',
  'Dietaries',
  'Not Listed Allergy Or Dietary',
  'Event Requirements',
];
const styleFields = ['Event Styles', 'Service Styles', 'Individually Packed', 'Cutlery'];
const extraFields = ['Current Product List', 'Needs Merchandising Equipment'];

export const customerFields = [
  'Full Name',
  'Company',
  'Phone',
  'Email',
  'Office Address',
  'Staff Size',
  'Extra Contact Information',
];

export const nonCustomerFields = [...eventFields, ...requirementFields, ...styleFields, ...extraFields];

export const requiredNewUserFields = ['first_name', 'last_name', 'email'];

export const getTimeAgo = (date) => formatDistanceToNow(new Date(date), { addSuffix: true, includeSeconds: true });

export const notificationSeverityOptions = {
  error: 'Errors',
  warning: 'Warnings',
  info: 'Informational',
};

export const getNotificationIcon = (notification) => {
  if (notification.event.includes('cancel')) {
    return 'cancelled';
  }
  if (notification.event.includes('reject')) {
    return 'supplierprofile';
  }
  if (notification.event.includes('team') || notification.event.includes('package')) {
    return 'team';
  }
  if (notification.event.includes('new') && notification.loggable_type === 'Order') {
    return 'new';
  }
  if (!notification.loggable_type) {
    return 'system';
  }
  return notification.loggable_type.toLowerCase();
};

export const getSliderType = (notification) => {
  if (notification.event === 'orders-auto-confirmed') {
    return 'OrdersAutoConfirmed';
  }
  if (notification.event === 'pending-orders') {
    return 'PendingOrders';
  }
  if (notification.event === 'upcoming-public-holiday') {
    return 'HolidayOrders';
  }
  if (notification.event === 'new-customer-registration') {
    return 'CustomerRegistration';
  }
  if (['Order', 'CustomerQuote', 'CustomerBudget'].includes(notification.loggable_type)) {
    return notification.loggable_type;
  }
  return null;
};

// sanitized fields passed to the update axios request (instead of the whole object)
export const supplierUpdateFields = [
  'is_searchable',
  'team_supplier',
  'markup',
  'commission_rate',
  'customer_profile_ids',
  'close_from',
  'close_to',
];
export const companyUpdateFields = ['name', 'payment_term_days', 'customer_profile_ids', 'accounting_software'];
export const invoiceUpdateFields = ['do_not_notify'];
export const userUpdateFields = ['firstname', 'lastname', 'email', 'password', 'password_confirmation'];

export const invoiceStatusOptions = {
  unpaid: 'Unpaid',
  overdue: 'Overdue',
  paid: 'Paid',
};

export const invoiceSummaryOptions = {
  current: 'Current',
  within_1_month: '< 1 month',
  more_than_1_month: '1 month',
  more_than_2_months: '2 months',
  more_than_3_months: '3 months',
  older: '> 4 months',
}
