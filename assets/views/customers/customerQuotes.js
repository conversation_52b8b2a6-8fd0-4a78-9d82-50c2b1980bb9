import { render } from 'react-dom';

import { Provider } from 'react-redux';
import QuotesApp from 'components/customer/quotes/QuotesApp';
import store from 'store/configureCustomerOrdersStore';
import adminContext from 'contexts/adminContext';
import appContext from 'contexts/appContext';

import 'react-responsive-modal/styles.css';

export default function customerQuotes($el, props) {
  render(
    <Provider store={store}>
      <appContext.Provider value={props}>
        <QuotesApp />
      </appContext.Provider>
    </Provider>,
    $el
  );
}
