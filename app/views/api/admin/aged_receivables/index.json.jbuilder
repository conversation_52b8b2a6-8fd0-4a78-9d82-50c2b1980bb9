json.array! @aged_receivables.each do |customer, customer_receivables|
  overdue_total = 0
  invoices = customer_receivables.map(&:invoices).flatten(1)
  next if invoices.blank?

  json.id customer.id
  json.customer_name customer.name
  json.company_name customer.company&.name || customer.company_name
  if (billing_details = customer&.billing_details.presence)
    json.email billing_details.email
    json.phone billing_details.phone
  end
  
  Admin::ListAgedReceivables::TIME_FRAMES_MAP.each do |key, label|
    receivable = customer_receivables.detect{|receivable| receivable.time_frame == key }
    overdue_total += receivable&.total || 0
    json.set! key, number_to_currency(receivable&.total, precision: 2)
  end

  json.total number_to_currency(overdue_total, precision: 2)

  json.invoices invoices.each do |invoice|
    json.extract! invoice, :id, :number
    json.dates [invoice.from_at, invoice.to_at].map{|date| date.to_s(:date_verbose) }
    json.due_at invoice.due_at.to_s(:date_verbose)
    json.total number_to_currency(invoice.amount_price, precision: 2)
  end

  if (customer_user = customer&.user.presence)
    json.customer_invoices_path sign_in_as_customer_path(customer_user, redirect_path: customer_invoices_path)
  end
end
