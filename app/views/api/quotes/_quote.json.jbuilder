json.extract! quote, :id, :kind, :status, :uuid
quote.form_data.except('type').each do |field, value|
  formatted_value = case
  when field == 'date'
    DateTime.parse(value).strftime('%I:%M%p on %d/%m/%Y')
  when value.is_a?(Array)
    value.join(', ')
  else
    value
  end
  json.set! field.titleize, formatted_value
end

orders = quote.orders.where(status: %w[new quoted amended confirmed delivered])
order_count = orders.count
json.number_of_orders order_count
if order_count > 0 
  json.status orders.where.not(status: 'quoted').present? ? 'order placed' : 'quoted'
end

json.created_at quote.created_at.to_s(:date_verbose)