:ruby
  accessible_sections = case
  when is_yordar_admin?
    [:all]
  when current_user.allow_all_supplier_access
    [:suppliers]
  when is_pantry_manager?
    %i[orders customers reports]
  when is_company_team_admin?
    %i[orders customers invoices reports]
  else
    []
  end

  admin_badge_options = case
  when is_account_manager?
    ['Account Manager', 'account-manager']
  when is_yordar_admin?
    ['Yordar Admin', 'super-admin']
  when current_user.allow_all_supplier_access
    ['Supplier Access','supplier-access']
  when is_pantry_manager?
    ['Pantry Manager', 'pantry-manager']
  when is_company_team_admin?
    ['Company Admin', 'company-team']
  else
    []
  end

  active_customers_paths = [customers_admin_path]
  active_suppliers_paths = [suppliers_admin_path]

  if current_user.allow_all_supplier_access
    active_suppliers_paths << admin_path
  else
    active_customers_paths << admin_path
  end
  invoices_path = [invoices_admin_path, invoice_summary_admin_path]
  report_paths = [reports_admin_path, pantry_managers_admin_path]

.customer-sticky-sidebar{ data: { view_sidebar_nav: true } }
  %aside.customer-area-sidebar.invert
    %div
      .customer-area-sidebar__image
        = link_to prismic_root_url do
          = image_tag 'logo.svg'
        - if Rails.env.staging?
          STAGING
    
      %div.admin-badge{ class: admin_badge_options[1]}
        %p #{admin_badge_options[0]}

      %ul.customer-area-sidebar__ul.vertical

        - if (%i[all customers] & accessible_sections).present?
          %li.customer-area-sidebar__li{ class: is_active_path(*active_customers_paths) }
            = link_to is_company_team_admin? ? 'Users' : 'Customers', customers_admin_path, class: 'customer-sidebar-link'

        - if (accessible_sections.include?(:all))
          %li.customer-area-sidebar__li{ class: is_active_path(admins_admin_path) }
            = link_to 'Yordar Admins', admins_admin_path, class: 'customer-sidebar-link'

        %li.customer-area-sidebar__li{ class: is_active_path(admin_notifications_path) }
          = link_to admin_notifications_path, class: 'customer-sidebar-link between-flex' do
            Notifications
            - if Rails.env.development?
              %span.notification-count{ class: ('hidden' if unread_notifications_count == 0) }
                = unread_notifications_count
            - else
              %span.notification-count{ data: { view_notifications_count: { count: unread_notifications_count }.to_json } }
                = unread_notifications_count

        - if (%i[all orders] & accessible_sections).present?
          %li.customer-area-sidebar__li{ class: is_active_path(orders_admin_path) }
            = link_to 'Orders', orders_admin_path, class: 'customer-sidebar-link'

        - if (%i[all custom_orders] & accessible_sections).present?
          %li.customer-area-sidebar__li{ class: is_active_path(custom_orders_admin_path) }
            = link_to 'Custom Orders', custom_orders_admin_path, class: 'customer-sidebar-link'

        - if (%i[all companies] & accessible_sections).present?
          %li.customer-area-sidebar__li{ class: is_active_path(companies_admin_path) }
            = link_to 'Companies', companies_admin_path, class: 'customer-sidebar-link'

        - if (%i[all suppliers] & accessible_sections).present?
          %li.customer-area-sidebar__li{ class: is_active_path(*active_suppliers_paths) }
            = link_to 'Suppliers', suppliers_admin_path, class: 'customer-sidebar-link'

        - if (%i[all invoices] & accessible_sections).present?
          %li.customer-area-sidebar__li.nested-list{ class: is_active_path(*invoices_path) }
            = link_to 'Invoices', invoices_admin_path, class: 'customer-sidebar-link customer-sidebar-link__nested'
            %ul.nested.vertical.menu{ class: should_be_hidden(*invoices_path) }
              %li{ class: is_active_path(invoices_admin_path) }
                = link_to 'Invoices', invoices_admin_path

              - if (accessible_sections.include?(:all))
                %li{ class: is_active_path(invoice_summary_admin_path) }
                  = link_to 'Aged Receivables', invoice_summary_admin_path

        - if (%i[all reports] & accessible_sections).present?
          %li.customer-area-sidebar__li.nested-list{ class: is_active_path(*report_paths) }
            = link_to 'Reports', reports_admin_path, class: 'customer-sidebar-link customer-sidebar-link__nested'
            %ul.nested.vertical.menu{ class: should_be_hidden(*report_paths) }
              %li{ class: is_active_path(reports_admin_path) }
                = link_to 'Spends', reports_admin_path

              - if (accessible_sections.include?(:all))
                %li{ class: is_active_path(pantry_managers_admin_path) }
                  = link_to 'Pantry Manager', pantry_managers_admin_path

        - if (accessible_sections.include?(:all))
          %li.customer-area-sidebar__li{ class: is_active_path(coupons_admin_path) }
            = link_to 'Coupons', coupons_admin_path, class: 'customer-sidebar-link'

        - if (accessible_sections.include?(:all))
          %li.customer-area-sidebar__li{ class: is_active_path(promotions_admin_path) }
            = link_to 'Promotions', promotions_admin_path, class: 'customer-sidebar-link'

        - if (accessible_sections.include?(:all))
          %li.customer-area-sidebar__li{ class: is_active_path(suburbs_admin_path) }
            = link_to 'Suburbs', suburbs_admin_path, class: 'customer-sidebar-link'

        - if (accessible_sections.include?(:all))
          %li.customer-area-sidebar__li{ class: is_active_path(holidays_admin_path) }
            = link_to 'Holidays', holidays_admin_path, class: 'customer-sidebar-link'

        - if (accessible_sections.include?(:all))
          %li.customer-area-sidebar__li{ class: is_active_path(reminders_admin_path) }
            = link_to 'Reminders', reminders_admin_path, class: 'customer-sidebar-link'

        - if (%i[all suppliers] & accessible_sections).present?
          %li.customer-area-sidebar__li{ class: is_active_path(rails_admin_path) }
            = link_to 'Old Admin', rails_admin_path, class: 'customer-sidebar-link', target: '_blank'

    %div
      .personalised
        .personalised__tag.circle-icon= customer_name_helper(:initials, current_user.name)
        %div
          %span.personalised__name.sidebar= customer_name_helper(:full, current_user.name)
