:ruby
  customer ||= session_profile
  order_paths = [customer_profile_path]
  team_order_creation_paths = [customer_new_team_orders_path, customer_new_recurring_team_orders_path]
  meal_plan_paths = [customer_team_orders_path, customer_contacts_path] + team_order_creation_paths
  if @order.present?
    order_paths << order_show_path(@order)
  end
  if @team_order && @team_order.persisted?
    meal_plan_paths << team_order_path(@team_order)
    meal_plan_paths << edit_team_order_path(@team_order)
  end
  meal_plan_paths << customer_meal_plans_path
  settings_paths = [customer_account_and_billing_path, customer_saved_addresses_path, customer_notification_preferences_path]
  settings_paths << customer_settings_path if is_admin?
  settings_paths << request_admin_access_path if !customer.company_team_admin?
  billing_paths = [customer_invoices_path, customer_purchase_orders_path, customer_payment_options_path]
  catering_suppliers_url = cookie_suburb.present? ? next_app_supplier_search_url(category_group: 'office-catering', state: cookie_suburb.state, suburb: cookie_suburb.name.gsub(/\s/, '-')) : '#supplier-search-office-catering'
  snack_suppliers_url = cookie_suburb.present? ? next_app_supplier_search_url(category_group: 'office-snacks', state: cookie_suburb.state, suburb: cookie_suburb.name.gsub(/\s/, '-')) : '#supplier-search-office-snacks'
  is_masquerading = can_access_admin_portal?

  quote_order_count = customer.orders.where(status: 'quoted').where('delivery_at > ?', Time.zone.now).count

.customer-sticky-sidebar{ data: { view_sidebar_nav: true } }
  %aside.customer-area-sidebar
    %div
      .customer-area-sidebar__image
        = link_to prismic_root_url do
          = image_tag 'logo.svg'
        - if Rails.env.staging?
          STAGING

      %ul.customer-area-sidebar__ul.vertical

        %li.customer-area-sidebar__li.nested-list.orders{ class: is_active_path(*order_paths) }
          = link_to 'Orders', customer_profile_path, class: 'customer-sidebar-link orders-list'
          %ul.order-list.nested.vertical.menu{ class: should_be_hidden(*order_paths) }
            %li.upcoming{ class: (params[:show_past].blank? ? is_active_path(customer_profile_path) : '') }
              = link_to 'Upcoming Orders', customer_profile_path
            %li.past{ class: (params[:show_past].present? ? is_active_path(customer_profile_path) : '') }
              = link_to 'Past Orders', customer_profile_path(show_past: true)
        %li.customer-area-sidebar__li
          = link_to 'Catering', catering_suppliers_url, class: 'customer-sidebar-link catering'
        %li.customer-area-sidebar__li
          = link_to 'Pantry', snack_suppliers_url, class: 'customer-sidebar-link snacks'

        %li.customer-area-sidebar__li.nested-list{ class: is_active_path(*meal_plan_paths) }
          = link_to 'Meal Plans', customer_meal_plans_path, class: 'customer-sidebar-link customer-sidebar-link__nested meal-plans-icon'
          %ul.nested.vertical.menu{ class: should_be_hidden(*meal_plan_paths)}
            %li{ class: is_active_path(customer_new_team_orders_path) }
              = link_to 'Individual Meals', customer_new_team_orders_path
            %li{ class: is_active_path(customer_meal_plans_path) }
              = link_to 'Shared Meals', customer_meal_plans_path
        
        %li.customer-area-sidebar__li{ class: is_active_path(customer_quotes_path) }
          = link_to customer_quotes_path, class: 'customer-sidebar-link quotes-list' do
            Quotes
            - if quote_order_count > 0
              %span.notification-count.quotes
                = quote_order_count

        %li.customer-area-sidebar__li{ class: is_active_path(customer_my_suppliers_path) }
          = link_to 'My Suppliers', customer_my_suppliers_path, class: 'customer-sidebar-link with-favourite-heart'

        %li.customer-area-sidebar__li.nested-list{ class: is_active_path(*billing_paths) }
          = link_to 'Billing', customer_invoices_path, class: 'customer-sidebar-link customer-sidebar-link__nested billing-list'
          %ul.nested.vertical.menu{ class: should_be_hidden(*billing_paths) }
            %li{ class: is_active_path(customer_invoices_path) }
              = link_to 'Invoices', customer_invoices_path

            %li{ class: is_active_path(customer_purchase_orders_path) }
              = link_to 'Purchase Orders', customer_purchase_orders_path

            %li{ class: is_active_path(customer_payment_options_path) }
              = link_to 'Credit Cards', customer_payment_options_path

        %li.customer-area-sidebar__li{ class: is_active_path(customer_reports_path) }
          = link_to 'Reports', customer_reports_path, class: 'customer-sidebar-link reports-list'
        %li.customer-area-sidebar__li{ class: is_active_path(customer_employee_surveys_path) }
          = link_to 'Surveys', customer_employee_surveys_path, class: 'customer-sidebar-link surveys-list'
        
        %li.customer-area-sidebar__li.nested-list{ class: is_active_path(*settings_paths) }
          = link_to 'Settings', customer_account_and_billing_path, class: 'customer-sidebar-link customer-sidebar-link__nested settings-list'
          %ul.nested.vertical.menu{ class: should_be_hidden(*settings_paths) }
            %li{ class: is_active_path(customer_account_and_billing_path) }
              = link_to 'Account Details', customer_account_and_billing_path
            %li{ class: is_active_path(customer_saved_addresses_path) }
              = link_to 'My Addresses', customer_saved_addresses_path
            - if is_admin?
              %li{ class: is_active_path(customer_settings_path) }
                = link_to 'Customer Settings', customer_settings_path
            %li{ class: is_active_path(customer_notification_preferences_path) }
              = link_to 'Notification Preferences', customer_notification_preferences_path
            - if !customer.company_team_admin?
              %li{ class: is_active_path(request_admin_access_path) }
                = link_to 'Become an Admin', request_admin_access_path


    %div
      .personalised
        .personalised__tag.circle-icon= customer_name_helper(:initials)
        %div
          %span.personalised__name.sidebar= customer_name_helper(:full)
    %div.submenu.auth-dash
      %ul.mobile-list
        - if is_masquerading
          %li.mobile-auth-dash
            %a.user-icon= link_to 'Admin Dash', sign_out_as_path
        %li.mobile-auth-dash
          %a.logout-icon= link_to 'Logout', destroy_user_session_path, method: :delete   
