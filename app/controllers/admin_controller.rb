class AdminController < ApplicationController

  before_action :authenticate_user!
  before_action :ensure_admin_portal_access
  before_action :ensure_yordar_admin, only: %i[admins pantry_managers coupons suburbs holidays promotions invoice_summary]

  layout 'admin'

  def dashboard
    render current_user.allow_all_supplier_access? ? 'suppliers' : 'customers'
  end

  def customers; end

  def admins; end

  def companies; end

  def suppliers; end

  def invoices; end

  def invoice_summary; end

  def orders; end

  def reports; end

  def pantry_managers; end

  def coupons; end

  def suburbs; end

  def holidays; end

  def promotions; end

  def notifications; end

end