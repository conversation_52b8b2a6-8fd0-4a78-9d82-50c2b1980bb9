class API::QuotesController < ApplicationController

  skip_before_action :verify_authenticity_token, if: :is_react_app?
  before_action :ensure_customer, only: :index
  before_action :fetch_customer

  def index
    @quotes = @customer.quotes
  end

  def show
    @quote = @customer.quotes.where(id: params[:id] || params[:customer_qoute_id]).first
  end

  def create
    quote_submitter = Customers::Quotes::Submit.new(form_data: quote_params[:form_data], profile: @customer).call
    if quote_submitter.success?
      head :ok
    else
      render json: quote_submitter.errors, status: :unprocessable_entity
    end
  end

  def customer
    if @customer.blank?
      render json: ['Could not find quote customer'], status: :unprocessable_entity
    end
  end

private

  def fetch_customer
    @customer = params[:uuid].present? ? CustomerProfile.where(uuid: params[:uuid]).first : session_profile
  end

  def quote_params
    params.permit(form_data: {})
  end

end