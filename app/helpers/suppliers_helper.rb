module SuppliersHelper
  def filter_categories_for(supplier:, category_group:)
    category_group ||= 'catering-services'
    Rails.cache.fetch(['filter-categories', supplier.cache_key, category_group]) do
      categories = Category.joins(:menu_sections).where(menu_sections: { supplier_profile_id: supplier.id, archived_at: nil })
      categories = categories.where(group: category_group) if category_group.present?
      categories.distinct
    end
  end

  def filter_dietaries_for(supplier)
    Suppliers::Cache::DietaryPreference::POSSIBLE_PREFERENCES.map do |preference|
      next if !supplier.send("has_#{preference}_items")

      preference.titleize
    end.reject(&:blank?)
  end

  def filter_business_codes_for(supplier)
    business_codes = []
    {
      is_socially_responsible: 'Social Enterprise',
      provides_contactless_delivery: 'Contactless Delivery',
      is_eco_friendly: 'Eco-Friendly',
      is_environmentally_accredited: 'Environmental Accreditation',
      is_indigenous_owned: 'Indigenous Owned',
      is_registered_charity: 'Registered Charity',
      is_female_owned: 'Female Owned',
      is_rainforest_alliance_certified: 'Rainforest Alliance',
    }.each { |field, label| business_codes << label if supplier.respond_to?(field) && supplier.send(field) }
    business_codes
  end

  def grouped_category_options_for_team_orders
    exluded_category_slugs = %w[
      afternoon-tea
      alcohol-catering
      breaky
      buffets
      finger-food
      healthy-catering
      individually-boxed-meals
      morning-tea
      seminars
      whole-cakes-and-sweets
      working-lunch
    ]
    {
      'catering-services': {
        button_text: 'Cuisines/Dietary',
        sections: {
          'Cuisines':
            filter_options(
              collection:
                Category.where(
                  group: 'catering-services', show_in_homepage: true
                )
                  .where
                  .not(slug: exluded_category_slugs)
                  .order(:weight, :name),
              name: 'category[]'
            ),
          'Dietary':
            filter_options(
              collection: [
                ['Gluten-Free Options', 'has_gluten_free_items'],
                ['Vegan Options', 'has_vegan_items'],
                ['Vegetarian Options', 'has_vegetarian_items']
              ],
              name: 'dietary[]'
            )
        }
      }
    }
  end

  def grouped_other_options_for_team_orders
    included_category_slugs = %w[afternoon-tea breaky morning-tea working-lunch]
    {
      'catering-services': {
        button_text: 'Mealtime/Others',
        sections: {
          'Mealtime':
            filter_options(
              collection:
                Category.where(
                  group: 'catering-services', show_in_homepage: true
                )
                  .where(slug: included_category_slugs)
                  .order(:weight, :name),
              name: 'category[]'
            ),
          'Other':
            filter_options(
              collection: [
                ['Contactless delivery', 'provides_contactless_delivery'],
                ['Social enterprise', 'is_socially_responsible'],
                ['Eco-Friendly', 'is_eco_friendly'],
                ['Environmental Accreditation', 'is_environmentally_accredited'],
                ['Indigenous Owned', 'is_indigenous_owned'],
                ['Registered Charity', 'is_registered_charity'],
                ['Female Owned', 'is_female_owned'],
                ['Rainforest Alliance', 'is_rainforest_alliance_certified'],
                ['Highest Rated', 'by_rating'],
                ['Next Day Delivery', 'lead_mode'],
                ['$0-100 min order', 'min_order'],
                ['Free Delivery', 'free_delivery', 'delivery[]']
              ],
              name: 'other[]'
            )
        }
      }
    }
  end

  def filter_options(collection:, name:)
    if !collection.is_a?(Array)
      collection = collection.map { |obj| [obj.name, obj.slug] }
    end
    new_filters = %w[individually-boxed-meals provides_contactless_delivery]
    if name == 'other[]' && params[:team_suppliers].present?
      collection << ['Team Order Supplier', 'true', 'team_suppliers', true]
    end
    collection.map do |label, value, override_name, is_fixed|
      OpenStruct.new(
        label: label,
        value: value,
        name: override_name || name,
        is_new: new_filters.include?(value),
        is_fixed: is_fixed || false
      )
    end
  end

  def available_features_for(supplier:, for_api: false)
    features = {
      has_vegetarian_items: {
        label: 'Vegetarian Options',
        icon: 'is_vegetarian',
        api_icon: 'vegetarian'
      },
      has_vegan_items: {
        label: 'Vegan options', icon: 'is_vegan', api_icon: 'vegan'
      },
      has_gluten_free_items: {
        label: 'Gluten Free', icon: 'is_gluten_free', api_icon: 'gluten'
      },
      team_supplier: {
        label: 'Team Ordering', icon: 'team_supplier', api_icon: 'team'
      },
      supplies_in_working_hours: {
        label: 'Working Hours', icon: 'working_hours', api_icon: 'working-hours'
      },
      needs_swipe_card_access: {
        label: 'After Hours',
        icon: 'needs_swipe_card_access',
        api_icon: 'after-hours'
      },
      provides_multi_service_point: {
        label: 'Multi Level Delivery',
        icon: 'multi_service_point',
        api_icon: 'multi-service'
      }
    }

    features.map do |field, detail|
      if supplier.send(field)
        supplier_detail = detail
        supplier_detail[:icon] = supplier_detail[:api_icon] if for_api
        supplier_detail.except(:api_icon)
      else
        nil
      end
    end.compact
  end

  def normalised_rating_count(rating_count)
    rating_count > 50 ? '50+' : rating_count.to_s
  end

  def next_app_category_group(category_group)
    return category_group if %w[office-catering office-snacks].include?(category_group)

    category_group ||= 'catering-services'
    category_group == 'catering-services' ? 'office-catering' : 'office-snacks'
  end

  def cannot_select_menu_item?
    return @_cannot_select_menu_item if !@_cannot_select_menu_item.nil?

    order = @supplier_order.present? && @supplier_order.order
    cannot_add_to_recurrent = order.present? &&
                              order.is_recurrent? &&
                              order.supplier_profiles.present? &&
                              order.supplier_profiles.exclude?(@supplier)
    is_supplier = session_profile.present? && session_profile.profile.is_supplier?
    is_view_only_team_order_menu = order.blank? && params[:team_order_menu].present?
    @_cannot_select_menu_item = cannot_add_to_recurrent || is_supplier || is_view_only_team_order_menu
  end

  def supplier_menu_team_order
    @_supplier_menu_team_order ||= @supplier_order&.order&.is_team_order? ? @supplier_order.order : nil
  end

  def team_order_pricing?
    @_team_order_pricing ||= supplier_menu_team_order.present? || params[:team_order_menu].present?
  end

  def current_order_budget
    @_current_order_budget ||=
      supplier_menu_team_order&.team_order_budget ||
      (params[:team_order_menu].present? && params[:budget].try(&:to_f))
  end

  def show_pricing?
    is_team_order = supplier_menu_team_order.present?
    is_team_admin = is_team_order && session_profile.present? && session_profile.team_admin? && supplier_menu_team_order.customer_profile == session_profile
    @_show_pricing ||= !is_team_order || (is_team_order && (!supplier_menu_team_order.hide_budget || is_team_admin))
  end

  def woolworths_store_id_for(suburb)
    Rails.configuration.woolworths.fulfilment_stores.mapped_stores.detect do |_, details|
      details[:state] == suburb.state
    end&.first
  end

  def maximum_available_quantity_for(menu_item)
    if @supplier.blank? || !@supplier.try(:woolworths?) || @suburb.blank?
      return nil
    end

    menu_item.woolworths_store_availabilities.detect do |availability|
      availability.store_id == woolworths_store_id_for(@suburb)
    end.stock_quantity
  end

  def liquor_license_info(supplier)
    return nil if supplier.liquor_license_no.blank?

    disclaimer = [
      'WARNING: Under the Liquor Control Reform Act 1998 it is an offence:',
      'To supply alcohol to a person under the age of 18 years (Penalty exceeds $6,000',
      'For a person under the age of 18 years to purchase or receive liquor (Penalty exceeds $500)'
    ].join(' ')
    licence_no = [
      "Liquor Licence: #{supplier.liquor_license_no}",
      supplier.abn_acn.present? ? "ABN: #{supplier.abn_acn}" : nil
    ].compact.join(' ')
    [disclaimer, licence_no]
  end
end
