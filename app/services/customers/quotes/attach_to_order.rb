class Customers::Quotes::AttachToOrder

  ORDER_FIELDS = {
    name: :occasion,
    number_of_people: :estimatedAttendees,
    contact_name: :fullName,
    phone: :phone,
    company_name: :company,
    contact_email: :email
  }

  def initialize(quote:, order:, order_params: {})
    @quote = quote
    @order = order
    @order_params = order_params
    @result = Result.new(order: order)
  end

  def call
    return result if !can_attach?

    update_order_with_quote_params

    result
  end

private

  attr_reader :quote, :order, :order_params, :result

  def can_attach?
    case
    when order.blank?
      result.errors << 'Cannot attach quote without an order'
    when quote.blank?
      result.errors << 'Cannot attach a missing customer quote'
    when order.customer_profile.present? && order.customer_profile != quote.customer_profile
      result.errors << 'You do not have access to this quote'
    end
    result.errors.blank?
  end

  def update_order_with_quote_params
    order_updater = Orders::Update.new(order: order, order_params: sanitized_params).call
    if order_updater.success?
      result.order = order_updater.order
    else
      result.errors += order_updater.errors
    end
  end

  def sanitized_params
    [
      quote_params,
      quote_order_details,
    ].inject(&:merge)
  end

  def quote_params
    {
      customer_quote_id: quote.id
    }
  end

  def quote_order_details
    form_data = quote.form_data
    return if form_data.blank?

    ORDER_FIELDS.map do |order_field, form_field|
      next if form_data[form_field.to_s].blank?

      [order_field, form_data[form_field.to_s]]
    end.reject(&:blank?).to_h
  end

  class Result
    attr_accessor :order, :errors

    def initialize(order:)
      @order = order
      @errors = []
    end

    def success?
      errors.blank? && order.customer_quote.present?
    end
  end
end