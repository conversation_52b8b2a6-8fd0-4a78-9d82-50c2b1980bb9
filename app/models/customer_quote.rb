# == Schema Information
#
# Table name: customer_quotes
#
#  id                  :bigint           not null, primary key
#  customer_profile_id :bigint
#  form_data           :jsonb
#  status              :string
#  kind                :string
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#
class CustomerQuote < ApplicationRecord

  VALID_QUOTE_KINDS = %w[catering snacks event].freeze
  VALID_STATUSES = %w[submitted notified accepted].freeze

  validates :kind, presence: true, inclusion: { in: VALID_QUOTE_KINDS }
  validates :status, presence: true, inclusion: { in: VALID_STATUSES }
  validates :form_data, presence: true
  validates :uuid, presence: true, uniqueness: true

  belongs_to :customer_profile
  has_many :orders

end
