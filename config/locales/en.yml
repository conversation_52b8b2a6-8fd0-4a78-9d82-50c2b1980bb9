# Files in the config/locales directory are used for internationalization
# and are automatically loaded by Rails. If you want to use locales other
# than English, add the necessary files in this directory.
#
# To use the locales, use `I18n.t`:
#
#     I18n.t 'hello'
#
# In views, this is aliased to just `t`:
#
#     <%= t('hello') %>
#
# To use a different locale, set it with `I18n.locale`:
#
#     I18n.locale = :es
#
# This would use the information in config/locales/es.yml.
#
# To learn more, please read the Rails Internationalization guide
# available at http://guides.rubyonrails.org/i18n.html.

# Sample localization file for English. Add more files in this directory for other locales.
# See https://github.com/svenfuchs/rails-i18n/tree/master/rails%2Flocale for starting points.

# Note, this file uses spaces, not tabs

en:
  admin:
    custom_order_fields:
      event_name: Event Name
      number_of_people: Number of People
      delivery_at: Delivery Date and time
      delivery_address: Delivery address
      delivery_instruction: Delivery Instruction
      suburb_id: Delivery Suburb
      contact_name: Contact Name
      contact_phone: Contact Phone
      credit_card_id: Payment Details
    actions:
      send_quote:
        menu: Quote Customer
        done: quoted to customer
      export_between_dates:
        breadcrumb: Quick export
        bulk_link: Quick export
        done: Exported
        link: Quick export
        menu: Quick export
        title: Quick export
      sign_in_as:
        breadcrumb: Sign in as
        bulk_link: Sign in as
        done: Signed in
        link: Sign in as
        menu: Sign in as
        title: Sign in as
      custom_order:
        breadcrumb: Custom order
        bulk_link: Custom order
        done: Custom order
        link: Custom order
        menu: Custom order
        title: Custom order
      deprecate_user:
        menu: Deprecate User
        title: Depracate User
        breadcrumb: Deprecate User
      favourite_customer:
        bulk_link: Set customer(s) as favourite
        menu: Set customer as favourite
      my_favourite_customers:
        menu: My Favourites
        title: My Favourite Customers
        breadcrumb: My Favourite Customers
      duplicate_supplier:
        breadcrumb: Duplicate Supplier
        bulk_link: Duplicate Supplier
        done: Supplier Duplicated
        link: Duplicate Supplier
        menu: Duplicate Supplier
        title: Duplicate Supplier
      dashboard:
        title: Yordar Dashboard
        menu: Dashboard
        breadcrumb: Dashboard
      history_index:
        title: ''
        menu: ''
        breadcrumb: ''
      form_report_all:
        menu: Reports
        title: Reports
        breadcrumb: Reports
        link: Reports
        bulk_link: Report on selected Forms
        done: Reports exported
      form_report:
        menu: Report
        title: Report
        breadcrumb: Report
        link: Report
        bulk_link: Report
        done: Report exported
      import:
        menu: Import
        title: Import
        breadcrumb: Import
        link: Import
        bulk_link: Import
        done: Imported
      reify:
        menu: Reify
        title: Reify
        breadcrumb: Reify
        link: Reify
      about:
        menu: About
        title: About
      accounting:
        menu: Accounting
        title: Accounting
        breadcrumb: Accounting
      reports:
        menu: Reports
        title: Reports
        breadcrumb: Reports
      clear_cache:
        menu: Clear Cache
        title: Clear Cache
        breadcrumb: Clear Cache
      new_admin:
        menu: New Admin Dash
        title: New Admin Dash
        breadcrumb: New Admin Dash
      organize:
        menu: Navigation
        title: Navigation
        breadcrumb: Navigation
        link: Navigation
        done: navigation
      deliver:
        menu: Deliver
        title: Deliver
        breadcrumb: Deliver
        link: Deliver
        done: delivered
      bulk_update:
        bulk_link: Bulk Update
    export:
      csv:
        header_for_association_methods: "%{association}.%{name}"
    misc:
      filter_date_format: 'dd/mm/yy'
      add_filter: Filter by ..
  order:
    order_variant:
      team_order: Team
      event_order: Event
      general: General
    status:
      draft: Draft
      pending: Pending
      new: New
      confirmed: Confirmed
      delivered: Delivered
      cancelled: Cancelled
      pending_cancelled: Cancelled
      skipped: Skipped
      amended: Amended
      rejected: Rejected
      awaiting_ffc_confirmation: Pending
      paused: On Hold
      quoted: Quoted
    billing_frequency:
      instantly: Per order
      weekly: Weekly
      monthly: Monthly
    payment_status:
      paid: 'Yes'
      unpaid: 'No'
      partial: Partial
      error: Error
    column_label:
      contact_name: Contact name
      phone: Contact phone
      company_name: Company name
      department_identity: Cost Centre ID
      delivery_at: Delivery date & time
      delivery_address: Delivery address
      delivery_address_level: Delivery address level
      delivery_instruction: Delivery instructions
      delivery_type: Delivery type
  team_order:
    errors:
      cancelled_event: This team order has been cancelled, and you can no-longer place/edit your order.
      cutoff_exceeded: Order cutoff time has been exceeded and you can no longer order via the system. Please contact Yordar for any further changes.
      order_closed: This team order is now closed for editing.
      missing_order: Could not find a matching team order.
      missing_order_attendee: The team order does not exist OR you don't have access to this team order.
    warnings:
      cutoff_almost_exceeded: Order cutoff time is approaching. You have a few minutes left before you can no longer confirm/edit your order.
      pending_order_confirmation: Order not confirmed yet!
    status:
      pending: Pending - Awaiting attendee orders
      cutoff_approaching: Pending - Awaiting attendee orders - cutoff approaching!
      grace_period: Order past cutoff - Contact Yordar Admin for further changes
      expired: 'Expired - Order is past supplier lead time'
      new: New - Submitted to supplier - changes no longer allowed
      amended: Amended
      confirmed: Confirmed
      delivered: Delivered
      cancelled: Order Cancelled
  order_line:
    status:
      accepted: Confirmed
      pending: Pending
      rejected: Rejected
      amended: Amended
      notified: Amended
  category:
    group:
      special-events: Special Events
      kitchen-supplies: Kitchen Supplies
      catering-services: Catering Services
      gifting: Gifts
      hints:
        catering-services: 'used to tag menus associated to food catering'
        kitchen-supplies: 'used to tag menus associated to office pantry supplies like milk, fruit, snack, etc'
  customer_profiles:
    settings:
      team_admin:
        label: Team Admin
        description: Allows Customers to place Team Orders
      company_team_admin:
        label: Company Team Admin
        description: Allows Customers to Access other customers within company (using admin permissions)
      cancel_review_requests:
        label: Do not send Order Review requests
        description: When set, the customer does not get the review request email for each Order
      has_gst_split_invoicing:
        label: Has GST split invoicing
        description: Requires disparate POs for Order Items so it could be invoiced separately
      requires_department_identity:
        label: Requires Cost Centre ID
        description: This customer requires a Cost Centre ID when paying on account (with format)
      requires_supplier_markup:
        label: Show Supplier Markup
        description: This shows the supplier markup (only for suppliers with commission) on Order PDFs
      requires_loading_dock_code:
        label: Requires Loading Dock Code
        description: This customer requires loading dock code for order deliveries
      default_orders_view:
        label: Default order view within dashboard
        description: How the customers view their orders within the customer dash
      accounting_software:
        label: Accounting Software used by the customer
        description: Adds it to invoice reference when uploading Invoice to Xero
    billing_details:
      order_summaries:
        label: Send Order Summaries
        description: Sends daily order summaries
      summary_report:
        label: Send Summary Report
        description: Sends a monthly (recurring) order report
      invoice_spreadsheet:
        label: Bulk Order Invoice Spreadsheet
        description: Sends a CSV version of the Tax Invoice
  supplier_profiles:
    provides_contactless_delivery:
      'true': 'provides Contactless Delivery'
      'false': 'does not provide Contactless Delivery'
    tags:
      is_new_expires_at:
        label: New Supplier Expiry Date
        description: Supplier is tagged as New up till the expiry date
      is_featured:
        label: Featured Supplier
        description: Supplier is tagged as Featured
      admin_only:
        label: Visible to Admins Only
        description: Supplier is only visible to admins
      can_manage_menu_dashboard:
        label: Menu Manager
        description: Can manage their own Menu
      needs_multi_day_summary:
        label: Multi Day Summary
        description: Sends Monday's order summary along with the Friday's Morning Summary
      needs_recurring_reminder:
        label: Recurring Reminder
        description: Sends individual recurring order reminder at lead time cutoff (mostly for catering suppliers)
      has_skus:
        label: Item have SKUs
        description: Allow menu management to add SKUs to the items
      uses_flex_catering:
        label: Uses Flex Catering
        description: Supplier will receive a JSON file along with PDF order details
      is_event_caterer:
        label: Event Caterer
        description: Tagged as being able to provide catering for Events
      needs_swipe_card_access:
        label: After Hours
        description: Can deliver after work hours
      supplies_in_working_hours:
        label: Working Hours
        description: Can deliver during work hours
      provides_contactless_delivery:
        label: Contactless Delivery
        description: Can provide contactless delivery
      provides_multi_service_point:
        label: Multi Service Point
        description: Has multiple service points
      is_socially_responsible:
        label: Social Enterprise
        description: Certified social enterprise by Social Trader or SEWF (Social Enterprise World Forum)
      is_indigenous_owned:
        label: Indigenous Owned
        description: Indigenous owned business certified by Supply Nation
      is_environmentally_accredited:
        label: Environmentally Accredited
        description: Has ISO 14001 Environmental Management Systems or other environmental accreditation
      is_registered_charity:
        label: Registered Charity 
        description: Registered charity with ACNC (Australian Charities and Not-for-profits Commission)
      is_female_owned:
        label: Female Owned
        description: Business with greater than 50% female ownership
      is_rainforest_alliance_certified:
        label: Rainforest Alliance Certified
        description: Has Rainforest Alliance certification
      is_eco_friendly:
        label: Eco-Friendly
        description: Tagged as being Eco-Friendly
      billing_frequency:
        label: Billing Frequency
        description: How frequently should the Supplier Invoices be generated (and uploaded to Xero)?
      payment_term_days:
        label: Payment Term Days
        description: Set due date for Supplier Invoices (RGI)
  notification_preference:
    customer-new_order:
      label: New Order
      description: Sent when a new order is successfully received by Yordar
    customer-order_confirmed:
      label: Order confirmation
      description: Sent when an order is confirmed by the supplier(s)
    customer-order_review_invitation:
      label: Order review invitation
      description: Invitation to review a recent order
    customer-monthly_summary_report:
      label: Monthly Summary report
      description: Summarized orders for the month
    # Customer Billing emails
    customer-order_invoice:
      label: Order Invoice
      description: Sent when an invoice is generated for orders
    customer-invoice_tax_receipt:
      label: Invoice Payment
      description: Sent when an invoice is paid for in Yordar App
    customer-order_summary:
      label: Order summary
      description: Summary of future recurring orders
    customer-public_holiday_orders:
      label: Public Holiday Orders
      description: Sent when orders that fall on a public holiday are automatically managed by Yordar
    # Team Order Admin emails
    customer-new_team_order:
      label: New Team Order
      description: Sent when a new team order is successfully received by Yordar. Contains vital information that can be used to manage the team order and attendees
    customer-new_team_order_package:
      label: New Team Order Package
      description: Sent when a new team order package is successfully received by Yordar. Contains vital information that can be used to manage the package and the underlying team orders
    team-order-admin-cutoff_notification: # 2hr and 30m
      2hr:
        label: Team Order Cutoff - 2hr
        description: Sent 2hrs before the team order supplier cutoff time
      30m:
        label: Team Order Cutoff - 30m
        description: Sent 30 minutes before the team order supplier cutoff time
    team-order-admin-team_order_extension:
      initial:
        label: Team Order Extension Email - Initial
        description: Sent on Wednesday to remind the team admin to select suppliers for 2nd to next week of Recurring Team Orders
      final:
        label: Team Order Extension Email - Final
        description: Sent on Friday to remind the team admin to select suppliers for 2nd to next week of Recurring Team Orders
    team-order-admin-anonymous_attendees_notification:
      label: Anonymous Attendee Notification
      description: Sent when team order contains anonymous antendee registrations that need to be approved, to be included in final order
    customer-team_order_submission:
      label: Team Order Submission
      description: Sent when the team order has been passed on to the supplier(s). May contain information about final order totals
    # Team Order Attendee emails
    team-order-attendee-invite:
      label: Attendee Invitation
      description: Sent to the attendee(s) when invited to a team . Contains unique link for the attendee(s) to place their orders
    team-order-attendee-package_invite:
      label: Attendee Package Invitation
      description: Sent to the attendee(s) when invited to a team package. Contains unique link for the attendee(s) to view the underlying team orders and place their orders
    team-order-attendee-removed_notification:
      label: Attendee Removed
      description: Sent to attendee(s) when their invitation to a team order is removed
    team-order-attendee-order_checkout:
      label: Attendee Checkout
      description: Sent to attendee(s) when their order is successfully captured by Yordar
    team-order-attendee-cutoff_notification: # 4hr, 2hr and 30m
      4hr:
        label: Team Order Attendee Cutoff - 4hr
        description: Sent 4hrs before the team order supplier cutoff time
      2hr:
        label: Team Order Attendee Cutoff - 2hr
        description: Sent 2hrs before the team order supplier cutoff time
      30m:
        label: Team Order Attendee Cutoff - 30m
        description: Sent 30 minutes before the team order supplier cutoff time
      24hr-recurring:
        label: Recurring Team Order Attendee Cutoff - 24hr
        description: Sent to all registered package attendees, a day before the team order supplier cutoff time
      4hr-recurring:
        label: Recurring Team Order Attendee Cutoff - 4hr
        description: Sent to all registered package attendees, 4hrs before the team order supplier cutoff time
      30m-recurring:
        label: Recurring Team Order Attendee Cutoff - 30m
        description: Sent to all registered package attendees, 30 minutes before the team order supplier cutoff time
    team-order-attendee-delivery_notification:
      label: Attendee Delivery
      description: Sent to attendee(s) 30m before delivery of team order
    team-order-attendee-order_cancelled:
      label: Team Order Cancelled
      description: Sent to attendee(s) if the team order is cancelled
    supplier-order_summary:
      daily:
        label: Daily Order Summary
        description: Sent every weekday at 3pm containing list of orders to be delivered the day after
      morning:
        label: Morning Order Summary
        description: Sent every weekday at 7am containing list of orders to be delivered later in the same day
    supplier-orders_reminder:
      label: Order Reminder
      description: Sent every week at 3pm containing  a list of recurring orders to be delivered in the next week
  event_logs:
    new-order-quoted: New Order Quoted
    new-order-submitted: New Order Submitted
    order-amended: Order Amended
    woolworths-checkout-failed: Woolworths Checkout Failed
    order-rejected: Order Rejected by Supplier
    order-canceled: Order Cancelled One-Off
    order-canceled-permanently: Order Cancelled Permanently
    on-hold-charge-failed: On Hold Charge Failed
    new-team-order-created: New Team Order Created
    new-package-created: New Team Order Pacakage Created
    package-extended: Team Order Package Extended
    approaching-cutoff: Team Order Aproaching Cutoff
    approaching-cutoff-below-minimum: Team Order Aproaching Cutoff Below Minimum
    custom-order-saved-as-draft: Custom Order Saved (as draft)
    new-custom-order-quoted: New Custom Order Quoted
    new-custom-order-submitted: New Custom Order Submitted
    order-below-margin-threshold: Order Margin is set below recommended value
    new-supplier-registration: New Supplier Registration
    supplier-agreement-signed: Supplier Agreement Signed
    searchable-updated: Supplier Searchable Changed
    margin-updated: Supplier Margin Updated
    new-customer-registration: New Customer Registration
    new-quote-submitted: New Customer Quote Submitted
    invoice-overdue: Invoice Overdue
    pending-orders: Pending Draft/Quoted Orders
    orders-auto-confirmed: Orders Auto-Comfirmed
    upcoming-public-holiday: Upcoming Public Holiday
  error:
    404:
      title: The page you were looking for doesn't exist (404).
      msg: You may have mistyped the address or the page may have moved.
    500:
      title: An error occurred internally (500).
      msg: We're working on getting this fixed as soon as possible.
