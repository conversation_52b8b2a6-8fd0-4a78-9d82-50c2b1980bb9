# used for Rack::Cors setup in the environment files
NEXT_APP_DOMAINS = [
  'localhost:\d{4}',
  'order.yordar-dev.com.au:\d{4}',
  'order.yordar-dev.nz:\d{4}',
  'order.staging.yordar.com.au',
  'order.staging.yordar.nz',
  'order.yordar.com.au',
  'order.yordar.nz',
  # '192.168.\d{1}.\d{3}:\d{4}' # for mobile testing
]

GATSBY_APP_DOMAINS = [
  'yordar.com.au',
  'yordar.nz',
  'www.yordar.com.au',
  'www.yordar.nz',
  'staging.yordar.com.au',
  'staging.yordar.nz',
]

NEXT_APP_ROUTES = {
  '/api/order_lines*' => {
    credentials: true,
    headers: :any,
    methods: [:post, :put, :delete]
  },
  '/api/orders/*' => {
    credentials: true,
    headers: :any,
    methods: [:get, :post, :put, :patch],
  },
  '/api/locations*' => {
    credentials: true,
    headers: :any,
    methods: [:post, :put, :patch, :delete],
  },
  '/api/favourite_menu_items*' => {
    credentials: true,
    headers: :any,
    methods: :any,
  },
  '/api/suppliers*' => {
    credentials: true,
    headers: :any,
    methods: :any,
  },
  '/api/favourite_suppliers*' => {
    credentials: true,
    headers: :any,
    methods: :any,
  },
  '/api/rate_cards' => {
    credentials: true,
    headers: :any,
    methods: [:get],
  },
  '/api/hubspot*' => {
    credentials: true,
    headers: :any,
    methods: [:get],
  },
  '/api/menu_sections*' => {
    credentials: true,
    headers: :any,
    methods: [:get],
  },
  '/api/menu_items*' => {
    credentials: true,
    headers: :any,
    methods: [:get],
  },
  '/cart/clear*' => {
    credentials: true,
    headers: :any,
    methods: [:get],
  },
  '/api/quotes*' => {
    credentials: true,
    headers: :any,
    methods: [:post]
  },
  '/api/suburbs*' => {
    credentials: true,
    headers: :any,
    methods: [:get],
  },
  '/api/stripe_credit_cards*' => {
    credentials: true,
    headers: :any,
    methods: [:post],
  },
  '/api/employee_surveys/*' => {
    credentials: true,
    headers: :any,
    methods: [:post]
  },
  '/team-orders/*' => {
    credentials: true,
    headers: :any,
    methods: [:get]
  },
  '/api/team_order_attendees/*' => {
    credentials: true,
    headers: :any,
    methods: [:post, :get]
  },
  '/api/loading_docks*' => {
    credentials: true,
    headers: :any,
    methods: [:post, :get]
  }
}


GATSBY_APP_ROUTES = {
  '/suppliers*' => {
    credentials: true,
    headers: :any,
    methods: [:get, :post, :options],
  },
  '/api/gatsby/*' => {
    credentials: true,
    headers: :any,
    methods: [:get],
  },
  '/api/hubspot*' => {
    credentials: true,
    headers: :any,
    methods: [:get],
  },
}
